import { headers } from "next/headers"

const ratelimit = new Map<string, { count: number; resetTime: number }>()

export async function checkRateLimit(identifier: string): Promise<boolean> {
  const limit = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "5")
  const windowMs = parseInt(process.env.RATE_LIMIT_WINDOW_MS || "60000")
  
  const now = Date.now()
  const userLimit = ratelimit.get(identifier)
  
  if (!userLimit || now > userLimit.resetTime) {
    ratelimit.set(identifier, {
      count: 1,
      resetTime: now + windowMs
    })
    return true
  }
  
  if (userLimit.count >= limit) {
    return false
  }
  
  userLimit.count++
  return true
}

export async function getRateLimitIdentifier(): Promise<string> {
  const headersList = headers()
  const forwarded = (await headersList).get("x-forwarded-for")
  const ip = forwarded ? forwarded.split(",")[0] : "127.0.0.1"
  return ip
}