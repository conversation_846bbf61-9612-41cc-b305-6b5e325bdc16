import { auth } from "@/../lib/auth"
import { redirect } from "next/navigation"
import { SignOutButton } from "@/components/auth/sign-out-button"

export default async function DashboardPage() {
  const session = await auth()
  
  // This check is redundant with middleware but adds an extra layer
  if (!session) {
    redirect("/login")
  }
  
  return (
    <div className="container mx-auto py-10">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Dashboard</h1>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">User Information</h2>
          <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Name
              </dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
                {session.user.name || "Not provided"}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Email
              </dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
                {session.user.email}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Email Verified
              </dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
                {session.user.emailVerified ? "Yes" : "No"}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Provider
              </dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
                {session.user.provider || "credentials"}
              </dd>
            </div>
          </dl>
        </div>
        
        <SignOutButton />
      </div>
    </div>
  )
}