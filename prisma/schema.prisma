// This is your Prisma schema file
// Docs: https://pris.ly/d/prisma-schema
// Try Prisma Accelerate for faster queries: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  passwordHash  String?
  emailVerified DateTime?
  image         String?
  provider      String?   @default("credentials")
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts            Account[]
  sessions            Session[]
  verificationTokens  VerificationToken[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime
  userId     String?
  user       User?    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([identifier, token])
  @@map("verification_tokens")
}
