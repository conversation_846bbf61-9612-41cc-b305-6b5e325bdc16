import { NextResponse } from "next/server"
import { z } from "zod"
import { sendMagicLink } from "@/../lib/email"
import { checkRateLimit, getRateLimitIdentifier } from "@/../lib/rate-limit"

const emailSchema = z.object({
  email: z.string().email(),
})

export async function POST(request: Request) {
  try {
    // Rate limiting
    const identifier = await getRateLimitIdentifier()
    const allowed = await checkRateLimit(identifier)
    
    if (!allowed) {
      return NextResponse.json(
        { message: "Too many requests. Please try again later." },
        { status: 429 }
      )
    }
    
    const body = await request.json()
    const { email } = emailSchema.parse(body)
    
    // Send magic link email
    await sendMagicLink(email)
    
    return NextResponse.json(
      { message: "Magic link sent to your email" },
      { status: 200 }
    )
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid email address" },
        { status: 400 }
      )
    }
    
    console.error("Magic link error:", error)
    return NextResponse.json(
      { message: "Failed to send magic link" },
      { status: 500 }
    )
  }
}