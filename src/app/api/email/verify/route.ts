import { NextResponse } from "next/server"
import { prisma } from "@/../lib/prisma"

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const token = searchParams.get("token")
  
  if (!token) {
    return NextResponse.redirect(new URL("/login?error=InvalidToken", request.url))
  }
  
  try {
    const verificationToken = await prisma.verificationToken.findUnique({
      where: { token },
      include: { user: true }
    })
    
    if (!verificationToken) {
      return NextResponse.redirect(new URL("/login?error=InvalidToken", request.url))
    }
    
    if (verificationToken.expires < new Date()) {
      await prisma.verificationToken.delete({
        where: { token }
      })
      return NextResponse.redirect(new URL("/login?error=TokenExpired", request.url))
    }
    
    // Update user's email verification status
    if (verificationToken.userId) {
      await prisma.user.update({
        where: { id: verificationToken.userId },
        data: { emailVerified: new Date() }
      })
    }
    
    // Delete the verification token
    await prisma.verificationToken.delete({
      where: { token }
    })
    
    return NextResponse.redirect(new URL("/login?verified=true", request.url))
  } catch (error) {
    console.error("Email verification error:", error)
    return NextResponse.redirect(new URL("/login?error=VerificationFailed", request.url))
  }
}