import nodemailer from "nodemailer"
import { prisma } from "./prisma"
import crypto from "crypto"

// Validate email configuration
if (!process.env.EMAIL_SERVER && process.env.NODE_ENV === "production") {
  throw new Error("EMAIL_SERVER environment variable is required in production")
}

const transporter = process.env.EMAIL_SERVER
  ? nodemailer.createTransport(process.env.EMAIL_SERVER)
  : nodemailer.createTransport({
    // Development fallback - use ethereal email for testing
    host: "smtp.ethereal.email",
    port: 587,
    auth: {
      user: "<EMAIL>",
      pass: "ethereal.pass"
    }
  })

export async function sendVerificationEmail(email: string, userId: string) {
  // Validate inputs
  if (!email || !userId) {
    throw new Error("Email and userId are required")
  }

  // Basic email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    throw new Error("Invalid email format")
  }

  const token = crypto.randomBytes(32).toString("hex")
  const expires = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours

  await prisma.verificationToken.create({
    data: {
      identifier: email,
      token,
      expires,
      userId,
    }
  })

  const verificationUrl = `${process.env.AUTH_URL}/api/email/verify?token=${token}`

  await transporter.sendMail({
    from: process.env.EMAIL_FROM,
    to: email,
    subject: "Verify your email address",
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Verify your email address</h2>
        <p>Click the link below to verify your email address:</p>
        <a href="${verificationUrl}" style="display: inline-block; padding: 10px 20px; background-color: #000; color: #fff; text-decoration: none; border-radius: 5px;">
          Verify Email
        </a>
        <p>Or copy and paste this link into your browser:</p>
        <p>${verificationUrl}</p>
        <p>This link will expire in 24 hours.</p>
      </div>
    `
  })
}

export async function sendMagicLink(email: string) {
  // Validate inputs
  if (!email) {
    throw new Error("Email is required")
  }

  // Basic email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    throw new Error("Invalid email format")
  }

  const token = crypto.randomBytes(32).toString("hex")
  const expires = new Date(Date.now() + 60 * 60 * 1000) // 1 hour

  await prisma.verificationToken.create({
    data: {
      identifier: email,
      token,
      expires,
    }
  })

  const magicLinkUrl = `${process.env.AUTH_URL}/api/auth/callback/email?token=${token}&email=${email}`

  await transporter.sendMail({
    from: process.env.EMAIL_FROM,
    to: email,
    subject: "Sign in to your account",
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Sign in to your account</h2>
        <p>Click the link below to sign in:</p>
        <a href="${magicLinkUrl}" style="display: inline-block; padding: 10px 20px; background-color: #000; color: #fff; text-decoration: none; border-radius: 5px;">
          Sign In
        </a>
        <p>Or copy and paste this link into your browser:</p>
        <p>${magicLinkUrl}</p>
        <p>This link will expire in 1 hour.</p>
      </div>
    `
  })
}