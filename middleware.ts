import { auth } from "@/../lib/auth"
import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

// Define public routes that don't require authentication
const publicRoutes = ["/login", "/signup"]
const authRoutes = ["/login", "/signup"]
const apiAuthPrefix = "/api/auth"
const DEFAULT_LOGIN_REDIRECT = "/dashboard"

export default auth((req) => {
  const { nextUrl } = req
  const isLoggedIn = !!req.auth
  
  // Check if current path is in our route lists
  const isApiAuthRoute = nextUrl.pathname.startsWith(apiAuthPrefix)
  const isPublicRoute = publicRoutes.includes(nextUrl.pathname)
  const isAuthRoute = authRoutes.includes(nextUrl.pathname)
  
  // Allow API auth routes (NextAuth endpoints)
  if (isApiAuthRoute) {
    return NextResponse.next()
  }
  
  // Redirect logged-in users away from auth pages
  if (isAuthRoute) {
    if (isLoggedIn) {
      return NextResponse.redirect(new URL(DEFAULT_LOGIN_REDIRECT, nextUrl))
    }
    return NextResponse.next()
  }
  
  // Protect all other routes - redirect to login if not authenticated
  if (!isLoggedIn && !isPublicRoute) {
    let callbackUrl = nextUrl.pathname
    if (nextUrl.search) {
      callbackUrl += nextUrl.search
    }
    
    const encodedCallbackUrl = encodeURIComponent(callbackUrl)
    return NextResponse.redirect(
      new URL(`/login?callbackUrl=${encodedCallbackUrl}`, nextUrl)
    )
  }
  
  return NextResponse.next()
})

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    "/((?!api/auth|api|_next/static|_next/image|favicon.ico).*)",
  ],
}
