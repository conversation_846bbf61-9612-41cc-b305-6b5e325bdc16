import { NextResponse } from "next/server"
import * as argon2 from "argon2"
import { prisma } from "@/../lib/prisma"
import { z } from "zod"
import { sendVerificationEmail } from "@/../lib/email"
import { checkRateLimit, getRateLimitIdentifier } from "@/../lib/rate-limit"

const signupSchema = z.object({
  name: z.string().min(2),
  email: z.string().email(),
  password: z.string().min(8),
})

export async function POST(request: Request) {
  try {
    // Rate limiting
    const identifier = await getRateLimitIdentifier()
    const allowed = await checkRateLimit(identifier)
    
    if (!allowed) {
      return NextResponse.json(
        { message: "Too many requests. Please try again later." },
        { status: 429 }
      )
    }
    
    const body = await request.json()
    const validatedData = signupSchema.parse(body)
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email }
    })
    
    if (existingUser) {
      return NextResponse.json(
        { message: "User with this email already exists" },
        { status: 400 }
      )
    }
    
    // Hash password
    const passwordHash = await argon2.hash(validatedData.password)
    
    // Create user
    const user = await prisma.user.create({
      data: {
        name: validatedData.name,
        email: validatedData.email,
        passwordHash,
        provider: "credentials",
      }
    })
    
    // Send verification email
    await sendVerificationEmail(user.email, user.id)
    
    return NextResponse.json(
      { 
        message: "Account created successfully. Please check your email to verify your account.",
        userId: user.id 
      },
      { status: 201 }
    )
  } catch (error) {
    console.error("Signup error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid input data", errors: error.issues },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
