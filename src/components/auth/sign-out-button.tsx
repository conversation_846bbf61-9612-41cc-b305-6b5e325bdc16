"use client"

import { signOut } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { LogOut } from "lucide-react"
import { useRouter } from "next/navigation"

export function SignOutButton() {
  const router = useRouter()
  
  const handleSignOut = async () => {
    await signOut({ 
      callbackUrl: "/login",
      redirect: true 
    })
    router.refresh()
  }
  
  return (
    <Button 
      onClick={handleSignOut}
      variant="outline"
      className="w-full sm:w-auto"
    >
      <LogOut className="mr-2 h-4 w-4" />
      Sign Out
    </Button>
  )
}