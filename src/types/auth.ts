// Authentication-related TypeScript types

export interface User {
  id: string
  email: string
  name?: string | null
  image?: string | null
  emailVerified: Date | null
  provider?: string
}

export interface AuthError {
  message: string
  code?: string
  field?: string
}

export interface SignupData {
  name: string
  email: string
  password: string
  confirmPassword: string
}

export interface LoginData {
  email: string
  password: string
  rememberMe?: boolean
}

export interface MagicLinkData {
  email: string
  callbackUrl?: string
}

export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  errors?: AuthError[]
}

export interface RateLimitInfo {
  allowed: boolean
  remaining?: number
  resetTime?: number
}

// Extend NextAuth types
declare module "next-auth" {
  interface Session {
    user: User
  }

  interface User {
    id: string
    email: string
    name?: string | null
    image?: string | null
    emailVerified: Date | null
    provider?: string
  }
}

declare module "@auth/core/jwt" {
  interface JWT {
    id: string
    emailVerified?: Date | null
    provider?: string
  }
}
