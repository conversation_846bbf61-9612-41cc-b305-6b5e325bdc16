import { headers } from "next/headers"
import { Redis } from "@upstash/redis"

// Use Redis for production, in-memory for development
const redis = process.env.UPSTASH_REDIS_REST_URL
  ? new Redis({
    url: process.env.UPSTASH_REDIS_REST_URL!,
    token: process.env.UPSTASH_REDIS_REST_TOKEN!,
  })
  : null

const ratelimit = new Map<string, { count: number; resetTime: number }>()

export async function checkRateLimit(identifier: string): Promise<boolean> {
  const limit = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "5")
  const windowMs = parseInt(process.env.RATE_LIMIT_WINDOW_MS || "60000")

  if (redis) {
    // Use Redis for production
    const key = `rate_limit:${identifier}`
    const current = await redis.get(key)

    if (!current) {
      await redis.setex(key, Math.ceil(windowMs / 1000), 1)
      return true
    }

    const count = parseInt(current as string)
    if (count >= limit) {
      return false
    }

    await redis.incr(key)
    return true
  } else {
    // Use in-memory for development
    const now = Date.now()
    const userLimit = ratelimit.get(identifier)

    if (!userLimit || now > userLimit.resetTime) {
      ratelimit.set(identifier, {
        count: 1,
        resetTime: now + windowMs
      })
      return true
    }

    if (userLimit.count >= limit) {
      return false
    }

    userLimit.count++
    return true
  }
}

export async function getRateLimitIdentifier(): Promise<string> {
  const headersList = headers()
  const forwarded = (await headersList).get("x-forwarded-for")
  const ip = forwarded ? forwarded.split(",")[0] : "127.0.0.1"
  return ip
}