{"name": "networking", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start"}, "dependencies": {"@auth/core": "^0.40.0", "@auth/prisma-adapter": "2.0.0", "@hookform/resolvers": "^5.2.2", "@prisma/client": "^6.16.2", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.15", "@sentry/nextjs": "^10", "@types/canvas-confetti": "^1.9.0", "@types/nodemailer": "^7.0.1", "@upstash/redis": "^1.35.4", "@vercel/analytics": "^1.5.0", "@vercel/edge-config": "^1.4.0", "@vercel/speed-insights": "^1.2.0", "argon2": "^0.44.0", "bcryptjs": "^3.0.2", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.544.0", "motion": "^12.23.16", "next": "15.5.3", "next-auth": "^5.0.0-beta.29", "nodemailer": "^7.0.6", "postcss": "^8.5.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.63.0", "react-icons": "^5.5.0", "tailwind-merge": "^3.3.1", "vercel": "^48.1.0", "zod": "^4.1.9"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.13", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "prisma": "^6.16.2", "tailwindcss": "^4.1.13", "tw-animate-css": "^1.3.8", "typescript": "^5"}}