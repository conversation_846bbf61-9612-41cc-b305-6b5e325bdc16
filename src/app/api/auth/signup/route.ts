import { NextResponse } from "next/server"
import * as argon2 from "argon2"
import { prisma } from "@/../lib/prisma"
import { z } from "zod"
import { sendVerificationEmail } from "@/../lib/email"
import { checkRateLimit, getRateLimitIdentifier } from "@/../lib/rate-limit"
import { sanitizeInput, validatePasswordStrength, applySecurityHeaders } from "@/../lib/security"

const signupSchema = z.object({
  name: z.string()
    .min(2, "Name must be at least 2 characters")
    .max(50, "Name must be less than 50 characters")
    .regex(/^[a-zA-Z\s]+$/, "Name can only contain letters and spaces")
    .transform(val => val.trim()),
  email: z.string()
    .email("Invalid email address")
    .max(255, "Email must be less than 255 characters")
    .transform(val => val.toLowerCase().trim()),
  password: z.string()
    .min(8, "Password must be at least 8 characters")
    .max(128, "Password must be less than 128 characters")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/[0-9]/, "Password must contain at least one number")
    .regex(/[^A-Za-z0-9]/, "Password must contain at least one special character"),
})

export async function POST(request: Request) {
  try {
    // Rate limiting
    const identifier = await getRateLimitIdentifier()
    const allowed = await checkRateLimit(identifier)

    if (!allowed) {
      const response = NextResponse.json(
        { message: "Too many requests. Please try again later." },
        { status: 429 }
      )
      return applySecurityHeaders(response)
    }

    const body = await request.json()
    const validatedData = signupSchema.parse(body)

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email }
    })

    if (existingUser) {
      // Don't reveal if user exists for security reasons
      const response = NextResponse.json(
        { message: "If this email is not already registered, you will receive a verification email." },
        { status: 200 }
      )
      return applySecurityHeaders(response)
    }

    // Hash password
    const passwordHash = await argon2.hash(validatedData.password)

    // Create user
    const user = await prisma.user.create({
      data: {
        name: validatedData.name,
        email: validatedData.email,
        passwordHash,
        provider: "credentials",
      }
    })

    // Send verification email
    await sendVerificationEmail(user.email, user.id)

    const response = NextResponse.json(
      {
        message: "Account created successfully. Please check your email to verify your account.",
        userId: user.id
      },
      { status: 201 }
    )

    return applySecurityHeaders(response)
  } catch (error) {
    // Log error securely (don't log sensitive data)
    console.error("Signup error:", {
      message: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    })

    if (error instanceof z.ZodError) {
      const response = NextResponse.json(
        {
          message: "Invalid input data",
          errors: error.issues.map(issue => ({
            field: issue.path.join('.'),
            message: issue.message
          }))
        },
        { status: 400 }
      )
      return applySecurityHeaders(response)
    }

    // Don't expose internal errors to client
    const response = NextResponse.json(
      { message: "An error occurred during registration. Please try again." },
      { status: 500 }
    )
    return applySecurityHeaders(response)
  }
}
