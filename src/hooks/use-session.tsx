"use client"

import { useSession as useNextAuthSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

interface UseSessionOptions {
  required?: boolean
  redirectTo?: string
  queryParams?: Record<string, string>
}

export function useSession({
  required = false,
  redirectTo = "/login",
  queryParams = {}
}: UseSessionOptions = {}) {
  const session = useNextAuthSession()
  const router = useRouter()
  
  useEffect(() => {
    if (required && session.status === "unauthenticated") {
      const params = new URLSearchParams(queryParams)
      const callbackUrl = window.location.pathname + window.location.search
      params.set("callbackUrl", callbackUrl)
      
      router.push(`${redirectTo}?${params.toString()}`)
    }
  }, [session.status, required, redirectTo, queryParams, router])
  
  return session
}